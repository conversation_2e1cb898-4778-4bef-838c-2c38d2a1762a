"use client"

import * as React from "react"
import { Moon, Sun } from "lucide-react"
import { Button } from "@/components/ui/button"

export function ThemeToggle() {
  const [theme, setThemeState] = React.useState<"light" | "dark" | "system">("system")

  React.useEffect(() => {
    try {
      const savedTheme = localStorage.getItem('theme') as "light" | "dark" | null
      if (savedTheme) {
        setThemeState(savedTheme)
      } else {
        const isDarkMode = document.documentElement.classList.contains("dark")
        setThemeState(isDarkMode ? "dark" : "light")
      }
    } catch (e) {
      const isDarkMode = document.documentElement.classList.contains("dark")
      setThemeState(isDarkMode ? "dark" : "light")
    }
  }, [])

  React.useEffect(() => {
    const isDark =
      theme === "dark" ||
      (theme === "system" &&
        window.matchMedia("(prefers-color-scheme: dark)").matches)
    document.documentElement.classList[isDark ? "add" : "remove"]("dark")

    // Save theme to localStorage (except for system)
    if (theme !== "system") {
      try {
        localStorage.setItem('theme', theme)
      } catch (e) {}
    }
  }, [theme])

  const toggleTheme = () => {
    setThemeState(prev => prev === "dark" ? "light" : "dark")
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={toggleTheme}
      aria-label="Toggle theme"
    >
      <Sun className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
      <Moon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
